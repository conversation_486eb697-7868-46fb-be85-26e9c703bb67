@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

.navbar {
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link {
    color: var(--light-color) !important;
    transition: color 0.3s;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
}

.container {
    max-width: 1200px;
}

/* RTL specific adjustments */
.navbar-nav {
    margin-right: auto;
}

/* Card styling */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* Button styling */
.btn {
    font-weight: 500;
    padding: 8px 16px;
}

/* Form styling */
.form-control {
    border-radius: 4px;
}

/* Table styling */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 60vh;
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('assets/images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
}

/* Blog Cards */
.blog-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    margin-bottom: 20px;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card img {
    height: 200px;
    object-fit: cover;
}

.blog-card .card-body {
    padding: 20px;
}

.blog-card .card-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.blog-card .card-text {
    color: #666;
    margin-bottom: 15px;
}

.category-badge {
    background-color: var(--secondary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 40px 0;
    margin-top: 50px;
}

.footer h5 {
    font-weight: bold;
    margin-bottom: 20px;
}

.footer ul {
    list-style: none;
    padding: 0;
}

.footer ul li {
    margin-bottom: 10px;
}

.footer ul li a {
    color: var(--light-color);
    text-decoration: none;
    transition: color 0.3s;
}

.footer ul li a:hover {
    color: var(--secondary-color);
}

.social-icons a {
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
    transition: color 0.3s;
}

.social-icons a:hover {
    color: var(--secondary-color);
}

/* Blog Post Styles */
.blog-header {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5));
    background-size: cover;
    background-position: center;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-bottom: 40px;
}

.blog-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.blog-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
}

.blog-meta {
    color: #666;
    margin-bottom: 30px;
}

.blog-meta span {
    margin-left: 15px;
}

.blog-meta i {
    margin-left: 5px;
}

.blog-image {
    width: 100%;
    max-height: 500px;
    object-fit: cover;
    border-radius: 10px;
    margin: 30px 0;
}

.blog-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

/* Related Posts */
.related-posts {
    margin-top: 50px;
    padding: 40px 0;
    background-color: #f8f9fa;
}

.related-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    margin-bottom: 20px;
}

.related-card:hover {
    transform: translateY(-5px);
}

.related-card img {
    height: 200px;
    object-fit: cover;
}

.related-card .card-body {
    padding: 20px;
}

.related-card .card-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        height: 40vh;
    }

    .blog-header {
        height: 30vh;
    }

    .blog-title {
        font-size: 2rem;
    }

    .blog-image {
        max-height: 300px;
    }

    .blog-card img {
        height: 150px;
    }
} 