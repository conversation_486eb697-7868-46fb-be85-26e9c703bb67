<?php
require_once 'config/db.php';
require_once 'includes/functions.php';

// Get selected category if any
$category_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get all categories with blog count
$stmt = $pdo->prepare("
    SELECT c.*, COUNT(b.id) as blog_count 
    FROM categories c 
    LEFT JOIN blogs b ON c.id = b.category_id AND b.status = 'published'
    GROUP BY c.id 
    ORDER BY c.name
");
$stmt->execute();
$categories = $stmt->fetchAll();

// Get blogs for selected category or latest blogs if no category selected
if ($category_id > 0) {
    $stmt = $pdo->prepare("
        SELECT b.*, c.name as category_name 
        FROM blogs b 
        LEFT JOIN categories c ON b.category_id = c.id 
        WHERE b.category_id = ? AND b.status = 'published'
        ORDER BY b.created_at DESC
    ");
    $stmt->execute([$category_id]);
    $blogs = $stmt->fetchAll();
    
    // Get category details
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $current_category = $stmt->fetch();
} else {
    $blogs = getLatestPosts(12); // Get more posts for the categories page
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التصنيفات - مصد | البلاغات والتحذيرات</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Tajawal) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root { --primary-color:#2c3e50; --secondary-color:#34495e; --accent-color:#3498db; }
        body { font-family:'Tajawal',sans-serif; background-color:#f8f9fa; }
        .navbar { background-color:var(--primary-color); padding:1rem 0; }
        .category-card { border:none; border-radius:15px; overflow:hidden; box-shadow:0 2px 15px rgba(0,0,0,0.1); transition:transform .3s; height:100%; }
        .category-card:hover { transform:translateY(-5px); }
        .category-card.active { border:2px solid var(--accent-color); }
        .category-count { background-color:var(--accent-color); color:#fff; padding:.25rem .75rem; border-radius:20px; font-size:.85rem; }
        .blog-card { border:none; box-shadow:0 2px 15px rgba(0,0,0,0.1); transition:transform .3s; border-radius:12px; overflow:hidden; height:100%; display:flex; flex-direction:column; }
        .blog-card:hover { transform:translateY(-5px); }
        .card-header { position:relative; padding:0; border:none; height:200px; overflow:hidden; }
        .card-img-top { width:100%; height:100%; object-fit:contain; background-color:#f8f9fa; padding:10px; }
        .category-badge { position:absolute; top:15px; right:15px; background-color:var(--accent-color); color:#fff; padding:5px 15px; border-radius:20px; font-size:.85rem; z-index:1; }
        .hero-section { background:linear-gradient(rgba(44,62,80,.9), rgba(52,73,94,.9)); color:#fff; padding:4rem 0; margin-bottom:3rem; text-align:center; }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <meta name="referrer" content="no-referrer-when-downgrade">
    <meta name="color-scheme" content="light">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2c3e50">
    <meta name="robots" content="index,follow">
    <link rel="canonical" href="<?php echo htmlspecialchars(base_url('categories.php' . ($category_id? ('?id='.(int)$category_id):'')), ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="description" content="<?php echo htmlspecialchars($category_id && !empty($current_category['description']) ? $current_category['description'] : 'استعرض التصنيفات والمنشورات المنشورة على مصد', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:locale" content="ar_AR">
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?php echo htmlspecialchars($category_id && !empty($current_category['name']) ? $current_category['name'] : 'التصنيفات', ENT_QUOTES, 'UTF-8'); ?> - مصد">
    <meta property="og:description" content="<?php echo htmlspecialchars($category_id && !empty($current_category['description']) ? $current_category['description'] : 'استعرض التصنيفات والمنشورات المنشورة على مصد', ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars(base_url('categories.php' . ($category_id? ('?id='.(int)$category_id):'')), ENT_QUOTES, 'UTF-8'); ?>">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">مصد | البلاغات والتحذيرات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link active" href="index.php">الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="categories.php">التصنيفات</a></li>
                    <li class="nav-item"><a class="nav-link" href="about.php">من نحن</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.php">اتصل بنا</a></li>
                </ul>
                <div class="d-flex">
                    <a href="complaint.php" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i> تقديم بلاغ جديد
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <?php if ($category_id && !empty($current_category)): ?>
                <h1 class="display-4 mb-3"><?php echo htmlspecialchars($current_category['name'], ENT_QUOTES, 'UTF-8'); ?></h1>
                <?php if (!empty($current_category['description'])): ?>
                <p class="lead"><?php echo htmlspecialchars($current_category['description'], ENT_QUOTES, 'UTF-8'); ?></p>
                <?php endif; ?>
            <?php else: ?>
                <h1 class="display-4 mb-3">التصنيفات والمنشورات</h1>
                <p class="lead">استعرض التصنيفات والمنشورات المنشورة على مصد</p>
            <?php endif; ?>
        </div>
    </section>

    <div class="container">
        <!-- Categories Grid -->
        <section class="mb-5">
            <div class="row g-4">
                <div class="col-12 <?php echo $category_id ? 'col-md-3' : 'col-md-12'; ?>">
                    <div class="row g-4">
                        <?php if (!$category_id): ?>
                            <div class="col-12 mb-4">
                                <h2>جميع التصنيفات</h2>
                            </div>
                        <?php endif; ?>
                        <?php foreach ($categories as $category): ?>
                        <div class="col-12 <?php echo $category_id ? 'col-12' : 'col-md-3'; ?>">
                            <div class="category-card card <?php echo $category_id == $category['id'] ? 'active' : ''; ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">
                                            <a href="?id=<?php echo (int)$category['id']; ?>" class="text-decoration-none text-dark">
                                                <?php echo htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>
                                            </a>
                                        </h5>
                                        <span class="category-count"><?php echo (int)$category['blog_count']; ?></span>
                                    </div>
                                    <?php if (!empty($category['description'])): ?>
                                    <p class="card-text text-muted mt-2 small">
                                        <?php echo htmlspecialchars($category['description'], ENT_QUOTES, 'UTF-8'); ?>
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <?php if ($category_id): ?>
                <div class="col-12 col-md-9">
                <?php endif; ?>

                <!-- Blogs Grid -->
                <?php if (!empty($blogs)): ?>
                    <div class="row g-4">
                        <?php if ($category_id): ?>
                            <div class="col-12 mb-4">
                                <h2>المنشورات ضمن التصنيف</h2>
                            </div>
                        <?php endif; ?>
                        <?php foreach ($blogs as $blog): ?>
                        <div class="col-md-4 mb-4">
                            <div class="blog-card h-100">
                                <div class="card-header">
                                    <?php if (!empty($blog['thumbnail_image'])): ?>
                                        <img src="uploads/thumbnail/<?php echo htmlspecialchars($blog['thumbnail_image'], ENT_QUOTES, 'UTF-8'); ?>" class="card-img-top" loading="lazy" alt="<?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php elseif (!empty($blog['featured_image'])): ?>
                                        <img src="uploads/featured/<?php echo htmlspecialchars($blog['featured_image'], ENT_QUOTES, 'UTF-8'); ?>" class="card-img-top" loading="lazy" alt="<?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php else: ?>
                                        <img src="<?php echo asset_url('assets/images/default-blog.jpg'); ?>" class="card-img-top" loading="lazy" alt="صورة البلاغ">
                                    <?php endif; ?>
                                    <?php if (!empty($blog['category_name'])): ?>
                                        <span class="category-badge"><?php echo htmlspecialchars($blog['category_name'], ENT_QUOTES, 'UTF-8'); ?></span>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?></h5>
                                    <p class="card-text text-muted">
                                        <?php 
                                            $content = strip_tags(html_entity_decode($blog['content']));
                                            echo mb_substr($content, 0, 100, 'UTF-8') . '...';
                                        ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div class="d-flex align-items-center">
                                            <i class="far fa-calendar-alt text-muted me-1"></i>
                                            <small class="text-muted"><?php echo date('Y/m/d', strtotime($blog['created_at'])); ?></small>
                                        </div>
                                        <a href="blog.php?id=<?php echo (int)$blog['id']; ?>" class="btn btn-outline-primary btn-sm">اقرأ المزيد</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">لا توجد منشورات في هذا التصنيف حالياً</div>
                <?php endif; ?>

                <?php if ($category_id): ?>
                </div>
                <?php endif; ?>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>عن مصد</h5>
                    <p>منصة متخصصة في نشر البلاغات والتحذيرات لحماية المجتمع من عمليات الاحتيال والنصب</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>تابعنا</h5>
                    <div class="social-icons">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> مصد - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
