<?php
require_once 'config/db.php';
require_once 'includes/functions.php';

// Get latest posts and categories
$posts = getLatestPosts(6);
$categories = getCategories();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصد | البلاغات والتحذيرات</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --danger-color: #dc3545;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: var(--primary-color);
            padding: 1rem 0;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .hero-section {
            background: linear-gradient(rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.9)), url('assets/images/hero-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 5rem 0;
            text-align: center;
            margin-bottom: 3rem;
        }
        .blog-card {
            border: none;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .blog-card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            position: relative;
            padding: 0;
            border: none;
            height: 200px;
            overflow: hidden;
        }
        .card-img-top {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: #f8f9fa;
            padding: 10px;
        }
        .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .card-text {
            flex-grow: 1;
            margin-bottom: 1rem;
        }
        .category-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: var(--accent-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            z-index: 1;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            line-height: 1.5;
        }
        .btn-outline-primary {
            border-radius: 20px;
            padding: 0.25rem 1rem;
            font-size: 0.875rem;
        }
        .btn-outline-primary:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 3rem;
        }
        .footer a {
            color: white;
            text-decoration: none;
        }
        .footer ul {
            list-style: none;
            padding: 0;
        }
        .footer ul li {
            margin-bottom: 0.5rem;
        }
        .social-icons a {
            margin-right: 1rem;
            font-size: 1.5rem;
        }
        .search-form .form-control {
            border-radius: 20px;
            padding-right: 1rem;
        }
        .search-form .btn {
            border-radius: 20px;
        }
        .gap-3 {
            gap: 1rem;
        }
        .dropdown-menu {
            min-width: 200px;
        }
        .dropdown-item {
            padding: 0.5rem 1rem;
        }
        .dropdown-item i {
            width: 20px;
            text-align: center;
            margin-left: 8px;
        }
        .copy-link:focus {
            outline: none;
            box-shadow: none;
        }
        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
        }
        .btn-danger i {
            margin-left: 0.5rem;
        }
    </style>
    <!-- Google Fonts (Tajawal) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">مصد | البلاغات والتحذيرات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">التصنيفات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="complaint.php" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i> تقديم بلاغ جديد
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-4">مرحباً بكم في موقع مصد</h1>
            <p class="lead mb-4">منصة موثوقة للبلاغات والتحذيرات لحماية المجتمع من الاحتيال</p>
            <a href="#latest-reports" class="btn btn-primary btn-lg">استعرض أحدث البلاغات</a>
            <a href="complaint.php" class="btn btn-danger btn-lg">تقديم بلاغ جديد</a>
        </div>
    </section>

    <!-- Latest Posts -->
    <section id="latest-reports" class="container py-5">
        <h2 class="text-center mb-4">أحدث البلاغات</h2>
        <div class="row">
            
            <?php if (!empty($posts)): ?>
                <?php foreach ($posts as $post): ?>
                <div class="col-md-4 mb-4">
                    <div class="blog-card h-100">
                        <div class="card-header p-0">
                            <?php if (!empty($post['thumbnail_image'])): ?>
                                <img src="uploads/thumbnail/<?php echo htmlspecialchars($post['thumbnail_image'], ENT_QUOTES, 'UTF-8'); ?>" class="card-img-top" loading="lazy" alt="<?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?>">
                            <?php elseif (!empty($post['featured_image'])): ?>
                                <img src="uploads/featured/<?php echo htmlspecialchars($post['featured_image'], ENT_QUOTES, 'UTF-8'); ?>" class="card-img-top" loading="lazy" alt="<?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?>">
                            <?php else: ?>
                                <img src="<?php echo asset_url('assets/images/default-blog.jpg'); ?>" class="card-img-top" loading="lazy" alt="صورة البلاغ">
                            <?php endif; ?>
                            <?php if (!empty($post['category_name'])): ?>
                                <span class="category-badge"><?php echo htmlspecialchars($post['category_name'], ENT_QUOTES, 'UTF-8'); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?></h5>
                            <p class="card-text text-muted">
                                <?php 
                                    // Remove HTML tags and decode entities before truncating
                                    $content = strip_tags(html_entity_decode($post['content']));
                                    echo mb_substr($content, 0, 100, 'UTF-8') . '...';
                                ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="d-flex align-items-center gap-3">
                                    <small class="text-muted">
                                        <i class="far fa-calendar-alt me-1"></i>
                                        <?php echo date('Y/m/d', strtotime($post['created_at'])); ?>
                                    </small>
                                    <small class="text-muted">
                                        <i class="far fa-eye me-1"></i>
                                        <?php echo number_format((int)$post['views']); ?>
                                    </small>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-share-alt"></i> مشاركة
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php
                                        $share_url = base_url('blog.php?id=' . (int)$post['id']);
                                        $share_text = $post['title'];
                                        ?>
                                        <li>
                                            <a class="dropdown-item" href="https://twitter.com/intent/tweet?url=<?php echo urlencode($share_url); ?>&text=<?php echo urlencode($share_text); ?>" target="_blank" rel="noopener noreferrer">
                                                <i class="fab fa-twitter text-info"></i> X (تويتر)
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://api.whatsapp.com/send?text=<?php echo rawurlencode($share_text . "\n" . $share_url); ?>" target="_blank" rel="noopener noreferrer">
                                                <i class="fab fa-whatsapp text-success"></i> واتساب
                                            </a>
                                        </li>
                                        <li>
                                            <button class="dropdown-item copy-link" data-url="<?php echo htmlspecialchars($share_url, ENT_QUOTES, 'UTF-8'); ?>" data-title="<?php echo htmlspecialchars($share_text, ENT_QUOTES, 'UTF-8'); ?>">
                                                <i class="fas fa-link text-secondary"></i> نسخ الرابط
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                                <a href="blog.php?id=<?php echo (int)$post['id']; ?>" class="btn btn-outline-primary btn-sm">اقرأ المزيد</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <p class="text-muted">لا توجد بلاغات حالياً</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="bg-light py-5">
        <div class="container">
            <h2 class="text-center mb-4">تصنيفات البلاغات</h2>
            <div class="row">
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                    <div class="col-md-3 col-sm-6 mb-4">
                        <div class="card h-100 text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h5>
                                <?php if (!empty($category['description'])): ?>
                                <p class="card-text text-muted"><?php echo htmlspecialchars($category['description']); ?></p>
                                <?php endif; ?>
                                <a href="category.php?id=<?php echo $category['id']; ?>" class="btn btn-outline-primary">عرض البلاغات</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">لا توجد تصنيفات حالياً</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5 class="mb-3">عن مصد</h5>
                    <p>منصة متخصصة في نشر البلاغات والتحذيرات لحماية المجتمع من عمليات الاحتيال والنصب</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5 class="mb-3">روابط سريعة</h5>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="about.php">من نحن</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5 class="mb-3">تابعنا</h5>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <hr class="mt-4">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle copy link buttons
        document.querySelectorAll('.copy-link').forEach(button => {
            button.addEventListener('click', function() {
                const url = this.dataset.url;
                const title = this.dataset.title;
                const text = title + '\n' + url;
                
                navigator.clipboard.writeText(text).then(() => {
                    // Change button text temporarily
                    const originalHtml = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check text-success"></i> تم النسخ';
                    setTimeout(() => {
                        this.innerHTML = originalHtml;
                    }, 2000);
                });
            });
        });
    });
    </script>
</body>
</html> 
