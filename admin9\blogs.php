<?php
session_start();
require_once '../config/db.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

require_once '../header.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $title = $_POST['title'];
                $slug = strtolower(str_replace(' ', '-', $title));
                $content = $_POST['content'];
                $category_id = $_POST['category_id'];
                $meta_description = $_POST['meta_description'];
                $status = $_POST['status'];
                
                // Handle image uploads
                $featured_image = '';
                $thumbnail_image = '';
                
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === 0) {
                    $featured_image = uploadImage($_FILES['featured_image'], 'featured');
                }
                
                if (isset($_FILES['thumbnail_image']) && $_FILES['thumbnail_image']['error'] === 0) {
                    $thumbnail_image = uploadImage($_FILES['thumbnail_image'], 'thumbnail');
                }
                
                if ($_POST['action'] === 'add') {
                    $stmt = $pdo->prepare("INSERT INTO blogs (title, slug, content, category_id, featured_image, thumbnail_image, meta_description, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$title, $slug, $content, $category_id, $featured_image, $thumbnail_image, $meta_description, $status]);
                } else {
                    $id = $_POST['id'];
                    $sql = "UPDATE blogs SET title = ?, slug = ?, content = ?, category_id = ?, meta_description = ?, status = ?";
                    $params = [$title, $slug, $content, $category_id, $meta_description, $status];
                    
                    if ($featured_image) {
                        $sql .= ", featured_image = ?";
                        $params[] = $featured_image;
                    }
                    if ($thumbnail_image) {
                        $sql .= ", thumbnail_image = ?";
                        $params[] = $thumbnail_image;
                    }
                    
                    $sql .= " WHERE id = ?";
                    $params[] = $id;
                    
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                }
                break;
            
            case 'delete':
                $id = $_POST['id'];
                $stmt = $pdo->prepare("DELETE FROM blogs WHERE id = ?");
                $stmt->execute([$id]);
                break;
        }
    }
}

// Function to handle image uploads
function uploadImage($file, $type) {
    $target_dir = "../uploads/" . $type . "/";
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    
    $file_extension = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
    $new_filename = uniqid() . '.' . $file_extension;
    $target_file = $target_dir . $new_filename;
    
    if (move_uploaded_file($file["tmp_name"], $target_file)) {
        return $new_filename;
    }
    return '';
}

// Get filter parameters
$search = isset($_GET['search']) ? $_GET['search'] : '';
$category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';

// Build the query
$query = "SELECT b.*, c.name as category_name FROM blogs b LEFT JOIN categories c ON b.category_id = c.id WHERE 1=1";
$params = [];

if (!empty($search)) {
    $query .= " AND (b.title LIKE ? OR b.content LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_id)) {
    $query .= " AND b.category_id = ?";
    $params[] = $category_id;
}

if (!empty($status)) {
    $query .= " AND b.status = ?";
    $params[] = $status;
}

$query .= " ORDER BY b.created_at DESC";

// Fetch filtered blogs
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$blogs = $stmt->fetchAll();

// Fetch statistics
$stats = [
    'total' => $pdo->query("SELECT COUNT(*) FROM blogs")->fetchColumn(),
    'published' => $pdo->query("SELECT COUNT(*) FROM blogs WHERE status = 'published'")->fetchColumn(),
    'draft' => $pdo->query("SELECT COUNT(*) FROM blogs WHERE status = 'draft'")->fetchColumn(),
    'categories' => $pdo->query("SELECT COUNT(DISTINCT category_id) FROM blogs")->fetchColumn(),
    'latest' => $pdo->query("SELECT COUNT(*) FROM blogs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn()
];

// Fetch all categories for the dropdown
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="mb-0">إدارة المقالات</h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBlogModal">
                <i class="fas fa-plus"></i> إضافة مقال جديد
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">إجمالي المقالات</h5>
                <h2 class="mb-0"><?php echo $stats['total']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">المنشورة</h5>
                <h2 class="mb-0"><?php echo $stats['published']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h5 class="card-title">المسودات</h5>
                <h2 class="mb-0"><?php echo $stats['draft']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">التصنيفات المستخدمة</h5>
                <h2 class="mb-0"><?php echo $stats['categories']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <h5 class="card-title">مقالات الأسبوع</h5>
                <h2 class="mb-0"><?php echo $stats['latest']; ?></h2>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" placeholder="بحث في المقالات..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="category_id">
                    <option value="">جميع التصنيفات</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>منشور</option>
                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i> تصفية
                </button>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>التصنيف</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($blogs as $blog): ?>
                            <tr>
                                <td><?php echo $blog['id']; ?></td>
                                <td>
                                    <?php if ($blog['thumbnail_image']): ?>
                                        <img src="../uploads/thumbnail/<?php echo $blog['thumbnail_image']; ?>" alt="" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد صورة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="fw-bold"><?php echo htmlspecialchars($blog['title']); ?></div>
                                    <small class="text-muted"><?php echo mb_substr(strip_tags($blog['content']), 0, 100) . '...'; ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo htmlspecialchars($blog['category_name'] ?? 'بدون تصنيف'); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $blog['status'] === 'published' ? 'success' : 'warning'; ?>">
                                        <?php echo $blog['status'] === 'published' ? 'منشور' : 'مسودة'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($blog['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $blog['id']; ?>">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $blog['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المقال؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Blog Modal -->
<div class="modal fade" id="addBlogModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مقال جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">عنوان المقال</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="content" class="form-label">محتوى المقال</label>
                                <textarea class="form-control" id="content" name="content" rows="10"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="meta_description" class="form-label">وصف مختصر</label>
                                <textarea class="form-control" id="meta_description" name="meta_description" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">التصنيف</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">اختر التصنيف</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="featured_image" class="form-label">الصورة الرئيسية</label>
                                        <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="thumbnail_image" class="form-label">الصورة المصغرة</label>
                                        <input type="file" class="form-control" id="thumbnail_image" name="thumbnail_image" accept="image/*">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">الحالة</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="draft">مسودة</option>
                                            <option value="published">منشور</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save"></i> حفظ المقال
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modals -->
<?php foreach ($blogs as $blog): ?>
<div class="modal fade" id="editModal<?php echo $blog['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المقال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" value="<?php echo $blog['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="edit_title<?php echo $blog['id']; ?>" class="form-label">عنوان المقال</label>
                                <input type="text" class="form-control" id="edit_title<?php echo $blog['id']; ?>" name="title" value="<?php echo htmlspecialchars($blog['title']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="edit_content<?php echo $blog['id']; ?>" class="form-label">محتوى المقال</label>
                                <textarea class="form-control" id="edit_content<?php echo $blog['id']; ?>" name="content" rows="10"><?php echo htmlspecialchars($blog['content']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="edit_meta_description<?php echo $blog['id']; ?>" class="form-label">وصف مختصر</label>
                                <textarea class="form-control" id="edit_meta_description<?php echo $blog['id']; ?>" name="meta_description" rows="3"><?php echo htmlspecialchars($blog['meta_description']); ?></textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="edit_category_id<?php echo $blog['id']; ?>" class="form-label">التصنيف</label>
                                        <select class="form-select" id="edit_category_id<?php echo $blog['id']; ?>" name="category_id" required>
                                            <option value="">اختر التصنيف</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" <?php echo $category['id'] == $blog['category_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الصورة الرئيسية الحالية</label>
                                        <?php if ($blog['featured_image']): ?>
                                            <img src="../uploads/featured/<?php echo $blog['featured_image']; ?>" alt="" class="img-fluid mb-2 rounded">
                                        <?php endif; ?>
                                        <input type="file" class="form-control" name="featured_image" accept="image/*">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">الصورة المصغرة الحالية</label>
                                        <?php if ($blog['thumbnail_image']): ?>
                                            <img src="../uploads/thumbnail/<?php echo $blog['thumbnail_image']; ?>" alt="" class="img-fluid mb-2 rounded">
                                        <?php endif; ?>
                                        <input type="file" class="form-control" name="thumbnail_image" accept="image/*">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="edit_status<?php echo $blog['id']; ?>" class="form-label">الحالة</label>
                                        <select class="form-select" id="edit_status<?php echo $blog['id']; ?>" name="status">
                                            <option value="draft" <?php echo $blog['status'] === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                            <option value="published" <?php echo $blog['status'] === 'published' ? 'selected' : ''; ?>>منشور</option>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save"></i> حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- TinyMCE Editor -->
<script src="https://cdn.tiny.cloud/1/a83fn1wd0u0ub1qg01f8ef9htioam0olkeh2kastgacqu81p/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
    tinymce.init({
        selector: '#content, textarea[id^="edit_content"]',
        language: 'ar',
        directionality: 'rtl',
        plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
        height: 400,
        images_upload_url: 'upload.php',
        automatic_uploads: true,
        file_picker_types: 'image',
        images_upload_handler: function (blobInfo, progress) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', 'upload.php');
                
                xhr.upload.onprogress = (e) => {
                    progress(e.loaded / e.total * 100);
                };
                
                xhr.onload = function() {
                    if (xhr.status === 403) {
                        reject({ message: 'Error 403: Forbidden', remove: true });
                        return;
                    }
                    
                    if (xhr.status < 200 || xhr.status >= 300) {
                        reject('Error uploading image');
                        return;
                    }
                    
                    const json = JSON.parse(xhr.responseText);
                    
                    if (!json || typeof json.location != 'string') {
                        reject('Invalid JSON: ' + xhr.responseText);
                        return;
                    }
                    
                    resolve(json.location);
                };
                
                xhr.onerror = function () {
                    reject('Image upload failed due to a XHR Transport error');
                };
                
                const formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());
                
                xhr.send(formData);
            });
        }
    });
</script>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 