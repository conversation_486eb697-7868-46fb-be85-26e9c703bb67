<?php
require_once 'config/db.php';
require_once 'includes/functions.php';

// Get post ID from URL
$post_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get post details
$stmt = $pdo->prepare("
    SELECT b.*, c.name as category_name, c.description as category_description 
    FROM blogs b 
    LEFT JOIN categories c ON b.category_id = c.id 
    WHERE b.id = ? AND b.status = 'published'
");
$stmt->execute([$post_id]);
$post = $stmt->fetch();

// If post not found, redirect to homepage
if (!$post) {
    header('Location: index.php');
    exit();
}

// Increment view count
$stmt = $pdo->prepare("UPDATE blogs SET views = views + 1 WHERE id = ?");
$stmt->execute([$post_id]);

// Get related posts from same category
$stmt = $pdo->prepare("
    SELECT b.*, c.name as category_name 
    FROM blogs b 
    LEFT JOIN categories c ON b.category_id = c.id 
    WHERE b.category_id = ? AND b.id != ? AND b.status = 'published' 
    ORDER BY b.created_at DESC 
    LIMIT 3
");
$stmt->execute([$post['category_id'], $post_id]);
$related_posts = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?> - مصد</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Tajawal) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: var(--primary-color);
            padding: 1rem 0;
        }
        .post-header {
            background: linear-gradient(rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.9));
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .post-image {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            margin: 2rem 0;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }
        .category-badge {
            background-color: var(--accent-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .post-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2c3e50;
        }
        .post-meta {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }
        .related-post {
            transition: transform 0.3s ease;
        }
        .related-post:hover {
            transform: translateY(-5px);
        }
        .share-buttons a {
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            margin: 0 5px;
            text-decoration: none;
            font-size: 0.9rem;
        }
        .share-buttons .facebook { background-color: #3b5998; }
        .share-buttons .twitter { background-color: #1da1f2; }
        .share-buttons .whatsapp { background-color: #25d366; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">مصد | البلاغات والتحذيرات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">التصنيفات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Post Header -->
    <header class="post-header">
        <div class="container">
            <?php if (!empty($post['category_name'])): ?>
            <span class="category-badge">
                <i class="fas fa-folder-open me-1"></i>
                <?php echo htmlspecialchars($post['category_name']); ?>
            </span>
            <?php endif; ?>
            <h1 class="display-4 mb-3"><?php echo htmlspecialchars($post['title']); ?></h1>
            <div class="post-meta">
                <i class="far fa-calendar-alt me-1"></i>
                <?php echo date('Y/m/d', strtotime($post['created_at'])); ?>
                &nbsp;&nbsp;
                <i class="far fa-eye me-1"></i>
                <?php echo number_format((int)$post['views']); ?> مشاهدة
            </div>
        </div>
    </header>

    <!-- Post Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-lg-8">
                <?php if (!empty($post['featured_image'])): ?>
                <img src="uploads/featured/<?php echo htmlspecialchars($post['featured_image'], ENT_QUOTES, 'UTF-8'); ?>" 
                     class="post-image" loading="lazy"
                     alt="<?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?>">
                <?php else: ?>
                <img src="<?php echo asset_url('assets/images/default-blog.jpg'); ?>" class="post-image" loading="lazy" alt="صورة البلاغ">
                <?php endif; ?>

                <div class="post-content mb-4">
                    <?php echo nl2br($post['content']); ?>
                </div>

                <!-- Share Buttons -->
                <div class="share-buttons mb-5">
                    <h5 class="mb-3">شارك البلاغ:</h5>
                    <?php
                    $canonical = base_url('post.php?id=' . (int)$post_id);
                    $share_url = urlencode($canonical);
                    $share_title = urlencode($post['title']);
                    ?>
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo $share_url; ?>" 
                       target="_blank" rel="noopener noreferrer"
                       class="facebook">
                        <i class="fab fa-facebook-f"></i> فيسبوك
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo $share_url; ?>&text=<?php echo $share_title; ?>" 
                       target="_blank" rel="noopener noreferrer"
                       class="twitter">
                        <i class="fab fa-twitter"></i> تويتر
                    </a>
                    <a href="https://api.whatsapp.com/send?text=<?php echo rawurlencode($post['title'] . "\n" . $canonical); ?>" 
                       target="_blank" rel="noopener noreferrer"
                       class="whatsapp">
                        <i class="fab fa-whatsapp"></i> واتساب
                    </a>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php if (!empty($related_posts)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">بلاغات مشابهة</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($related_posts as $related): ?>
                        <div class="related-post mb-3">
                            <a href="post.php?id=<?php echo (int)$related['id']; ?>" class="text-decoration-none">
                                <div class="row g-0">
                                    <?php if (!empty($related['thumbnail_image'])): ?>
                                    <div class="col-4">
                                        <img src="uploads/thumbnail/<?php echo htmlspecialchars($related['thumbnail_image'], ENT_QUOTES, 'UTF-8'); ?>" 
                                             class="img-fluid rounded" loading="lazy"
                                             alt="<?php echo htmlspecialchars($related['title'], ENT_QUOTES, 'UTF-8'); ?>">
                                    </div>
                                    <?php endif; ?>
                                    <div class="<?php echo !empty($related['thumbnail_image']) ? 'col-8' : 'col-12'; ?> ps-3">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($related['title'], ENT_QUOTES, 'UTF-8'); ?></h6>
                                        <small class="text-muted">
                                            <i class="far fa-calendar-alt"></i>
                                            <?php echo date('Y/m/d', strtotime($related['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>عن مصد</h5>
                    <p>منصة متخصصة في نشر البلاغات والتحذيرات لحماية المجتمع من عمليات الاحتيال والنصب</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>تابعنا</h5>
                    <div class="social-icons">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> مصد - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
