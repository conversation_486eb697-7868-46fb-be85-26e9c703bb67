<?php
require_once 'config/db.php';

// Get categories for dropdown
$stmt = $pdo->prepare("SELECT * FROM categories ORDER BY name");
$stmt->execute();
$categories = $stmt->fetchAll();

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate captcha
    if (!isset($_POST['captcha']) || strtolower($_POST['captcha']) !== 'مصد') {
        $errors[] = 'كلمة التحقق غير صحيحة';
    }

    // Validate required fields
    if (empty($_POST['name'])) {
        $errors[] = 'الرجاء إدخال الاسم';
    }
    if (empty($_POST['mobile'])) {
        $errors[] = 'الرجاء إدخال رقم الجوال';
    }
    if (empty($_POST['category_id'])) {
        $errors[] = 'الرجاء اختيار نوع البلاغ';
    }
    if (empty($_POST['link'])) {
        $errors[] = 'الرجاء إدخال الرابط';
    }
    if (empty($_POST['details'])) {
        $errors[] = 'الرجاء إدخال تفاصيل البلاغ';
    }

    // Handle file upload
    $attachment = '';
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'pdf'];
        $filename = $_FILES['attachment']['name'];
        $filetype = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (!in_array($filetype, $allowed)) {
            $errors[] = 'نوع الملف غير مسموح به. الأنواع المسموحة: JPG, JPEG, PNG, PDF';
        } else {
            $upload_dir = 'uploads/complaints/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $new_filename = uniqid() . '.' . $filetype;
            if (move_uploaded_file($_FILES['attachment']['tmp_name'], $upload_dir . $new_filename)) {
                $attachment = $new_filename;
            } else {
                $errors[] = 'حدث خطأ أثناء رفع الملف';
            }
        }
    }

    // If no errors, insert into database
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO complaints (name, mobile, category_id, link, details, attachment, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
            ");
            $stmt->execute([
                $_POST['name'],
                $_POST['mobile'],
                $_POST['category_id'],
                $_POST['link'],
                $_POST['details'],
                $attachment
            ]);
            $success = true;
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء حفظ البلاغ';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقديم بلاغ جديد - مصد | البلاغات والتحذيرات</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Tajawal) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: var(--primary-color);
            padding: 1rem 0;
        }
        .hero-section {
            background: linear-gradient(rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.9));
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
            text-align: center;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .form-label {
            font-weight: 500;
        }
        .required::after {
            content: ' *';
            color: red;
        }
        .custom-file-button input[type=file] {
            margin-left: -2px !important;
        }
        .custom-file-button input[type=file]::-webkit-file-upload-button {
            display: none;
        }
        .custom-file-button input[type=file]::file-selector-button {
            display: none;
        }
        .custom-file-button label {
            color: #fff;
            background-color: var(--accent-color);
            padding: 0.5rem 1rem;
            border-radius: 0.3rem;
            cursor: pointer;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">مصد | البلاغات والتحذيرات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">التصنيفات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="complaint.php" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i> تقديم بلاغ جديد
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 mb-3">تقديم بلاغ جديد</h1>
            <p class="lead">ساعدنا في حماية المجتمع من خلال الإبلاغ عن عمليات النصب والاحتيال</p>
        </div>
    </section>

    <div class="container mb-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <?php if ($success): ?>
                <div class="alert alert-success mb-4" role="alert">
                    <h4 class="alert-heading"><i class="fas fa-check-circle"></i> تم تقديم البلاغ بنجاح!</h4>
                    <p>شكراً لك على المساهمة في حماية المجتمع. سيتم مراجعة البلاغ من قبل فريقنا.</p>
                    <hr>
                    <p class="mb-0">يمكنك العودة إلى <a href="index.php" class="alert-link">الصفحة الرئيسية</a> أو تقديم <a href="complaint.php" class="alert-link">بلاغ جديد</a>.</p>
                </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger mb-4">
                    <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> يرجى تصحيح الأخطاء التالية:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <div class="form-card">
                    <form method="POST" enctype="multipart/form-data" novalidate>
                        <div class="mb-3">
                            <label for="name" class="form-label required">الاسم</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="mobile" class="form-label required">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="mobile" name="mobile" required
                                   pattern="[0-9]{10}" placeholder="+96800"
                                   value="<?php echo isset($_POST['mobile']) ? htmlspecialchars($_POST['mobile']) : ''; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="category_id" class="form-label required">نوع البلاغ</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">اختر نوع البلاغ</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo isset($_POST['category_id']) && $_POST['category_id'] == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="link" class="form-label required">الرابط</label>
                            <input type="url" class="form-control" id="link" name="link" 
                                   placeholder="https://"
                                   value="<?php echo isset($_POST['link']) ? htmlspecialchars($_POST['link']) : ''; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="details" class="form-label required">تفاصيل البلاغ</label>
                            <textarea class="form-control" id="details" name="details" rows="5" required
                                      placeholder="يرجى ذكر تفاصيل البلاغ بشكل واضح..."><?php echo isset($_POST['details']) ? htmlspecialchars($_POST['details']) : ''; ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="attachment" class="form-label">إرفاق ملف</label>
                            <div class="input-group custom-file-button">
                                <label class="input-group-text" for="attachment">اختر ملف</label>
                                <input type="file" class="form-control" id="attachment" name="attachment"
                                       accept=".jpg,.jpeg,.png,.pdf">
                            </div>
                            <div class="form-text">الملفات المسموحة: JPG, JPEG, PNG, PDF. الحد الأقصى: 5MB</div>
                        </div>

                        <div class="mb-4">
                            <label for="captcha" class="form-label required">كلمة التحقق</label>
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="captcha" name="captcha" required
                                           placeholder="اكتب كلمة 'مصد'">
                                </div>
                                <div class="col-md-6">
                                    <div class="form-text">للتحقق من أنك إنسان، يرجى كتابة كلمة 'مصد'</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> إرسال البلاغ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>عن مصد</h5>
                    <p>منصة متخصصة في نشر البلاغات والتحذيرات لحماية المجتمع من عمليات الاحتيال والنصب</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>تابعنا</h5>
                    <div class="social-icons">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> مصد - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // File input validation
    document.getElementById('attachment').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) { // 5MB
                alert('حجم الملف كبير جداً. الحد الأقصى هو 5 ميجابايت.');
                this.value = '';
            }
        }
    });

    // Mobile number validation
    document.getElementById('mobile').addEventListener('input', function(e) {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length > 10) {
            this.value = this.value.slice(0, 10);
        }
    });
    </script>
</body>
</html> 