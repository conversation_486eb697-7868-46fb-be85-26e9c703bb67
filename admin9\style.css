@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* Navbar Styles */
.navbar {
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link {
    color: var(--light-color) !important;
    transition: color 0.3s;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Table Styles */
.table th, .table td {
    vertical-align: middle;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #fff;
    border-bottom: none;
    padding: 20px;
}

/* Form Styles */
.form-control {
    border-radius: 5px;
    padding: 12px;
}

.btn {
    border-radius: 5px;
    padding: 12px;
}

/* Modal Styles */
.modal-content {
    border-radius: 10px;
}

.modal-header {
    border-bottom: none;
    padding: 20px;
}

.modal-footer {
    border-top: none;
    padding: 20px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .table-responsive {
        margin-bottom: 20px;
    }
} 