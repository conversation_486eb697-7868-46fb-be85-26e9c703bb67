<?php
require_once 'config/db.php';
require_once 'includes/functions.php';

// Get blog ID from URL
$blog_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($blog_id <= 0) {
    header('Location: index.php');
    exit();
}

// Increment view count
$stmt = $pdo->prepare("UPDATE blogs SET views = views + 1 WHERE id = ?");
$stmt->execute([$blog_id]);

// Get blog post details
$stmt = $pdo->prepare("
    SELECT b.*, c.name as category_name 
    FROM blogs b 
    LEFT JOIN categories c ON b.category_id = c.id 
    WHERE b.id = ? AND b.status = 'published'
");
$stmt->execute([$blog_id]);
$blog = $stmt->fetch();

if (!$blog) {
    header('Location: index.php');
    exit();
}

// Get related blogs
$stmt = $pdo->prepare("
    SELECT b.*, c.name as category_name 
    FROM blogs b 
    LEFT JOIN categories c ON b.category_id = c.id 
    WHERE b.category_id = ? AND b.id != ? AND b.status = 'published'
    ORDER BY b.created_at DESC 
    LIMIT 3
");
$stmt->execute([$blog['category_id'], $blog_id]);
$related_blogs = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?> - مصد</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Tajawal) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <?php
    // Build canonical/share data
    $canonical_url = base_url('blog.php?id=' . (int)$blog_id);
    $og_title = $blog['title'];
    $og_desc_src = strip_tags(html_entity_decode($blog['content']));
    $og_desc = mb_substr($og_desc_src, 0, 160, 'UTF-8');
    $og_image = !empty($blog['featured_image'])
        ? base_url('uploads/featured/' . $blog['featured_image'])
        : (!empty($blog['thumbnail_image'])
            ? base_url('uploads/thumbnail/' . $blog['thumbnail_image'])
            : base_url('assets/images/default-blog.jpg'));
    ?>
    <link rel="canonical" href="<?php echo htmlspecialchars($canonical_url, ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="description" content="<?php echo htmlspecialchars($og_desc, ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:locale" content="ar_AR">
    <meta property="og:type" content="article">
    <meta property="og:title" content="<?php echo htmlspecialchars($og_title, ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($og_desc, ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($canonical_url, ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($og_image, ENT_QUOTES, 'UTF-8'); ?>">
    <meta property="og:site_name" content="مصد">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($og_title, ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($og_desc, ENT_QUOTES, 'UTF-8'); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($og_image, ENT_QUOTES, 'UTF-8'); ?>">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: var(--primary-color);
            padding: 1rem 0;
        }
        .blog-header {
            background: linear-gradient(rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.9));
            color: white;
            padding: 5rem 0;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        .blog-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('uploads/featured/<?php echo htmlspecialchars($blog['featured_image'], ENT_QUOTES, 'UTF-8'); ?>');
            background-size: cover;
            background-position: center;
            opacity: 0.3;
            z-index: -1;
        }
        .blog-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .blog-image {
            width: 100%;
            max-height: 500px;
            object-fit: contain;
            border-radius: 10px;
            margin: 1rem 0;
            background-color: #f8f9fa;
            padding: 10px;
        }
        .blog-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2c3e50;
        }
        .category-badge {
            background-color: var(--accent-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .share-buttons {
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .share-buttons .btn {
            margin: 0.5rem;
            border-radius: 20px;
            padding: 0.5rem 1.5rem;
            font-size: 0.9rem;
        }
        .btn-twitter {
            background-color: #1DA1F2;
            color: white;
        }
        .btn-whatsapp {
            background-color: #25D366;
            color: white;
        }
        .btn-copy {
            background-color: #6c757d;
            color: white;
        }
        .related-card {
            border: none;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
            height: 100%;
        }
        .related-card:hover {
            transform: translateY(-5px);
        }
        .related-card img {
            height: 200px;
            object-fit: contain;
            background-color: #f8f9fa;
            padding: 10px;
        }
        .blog-stats {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .blog-stats i {
            margin-left: 5px;
            color: var(--accent-color);
        }
        .blog-stats span {
            display: inline-flex;
            align-items: center;
        }
    </style>
</head>
<body>
   <!-- Navbar -->
   <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">مصد | البلاغات والتحذيرات</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">التصنيفات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="complaint.php" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle"></i> تقديم بلاغ جديد
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Blog Header -->
    <header class="blog-header">
        <div class="container text-center">
            <?php if (!empty($blog['category_name'])): ?>
            <span class="category-badge">
                <i class="fas fa-folder-open me-1"></i>
                <?php echo htmlspecialchars($blog['category_name'], ENT_QUOTES, 'UTF-8'); ?>
            </span>
            <?php endif; ?>
            <h1 class="display-4 mb-3"><?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?></h1>
            <div class="text-white-50">
                <span class="me-3">
                    <i class="far fa-calendar-alt me-1"></i>
                    <?php echo date('Y/m/d', strtotime($blog['created_at'])); ?>
                </span>
            </div>
        </div>
    </header>

    <!-- Blog Content -->
    <main class="container">
        <div class="row">
            <div class="col-lg-8">
                <article class="blog-content">
                    <?php if (!empty($blog['featured_image'])): ?>
                    <div class="text-center mb-4">
                        <img src="uploads/featured/<?php echo htmlspecialchars($blog['featured_image'], ENT_QUOTES, 'UTF-8'); ?>" 
                             class="blog-image" loading="lazy"
                             alt="<?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?>">
                        <div class="mt-2">
                            <a href="uploads/featured/<?php echo htmlspecialchars($blog['featured_image'], ENT_QUOTES, 'UTF-8'); ?>" 
                               class="btn btn-sm btn-outline-secondary" 
                               download="<?php echo htmlspecialchars($blog['title'], ENT_QUOTES, 'UTF-8'); ?>">
                                <i class="fas fa-download"></i> تحميل الصورة
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="blog-text">
                        <?php echo $blog['content']; ?>
                    </div>

                    <!-- Share Buttons and Stats -->
                    <div class="share-buttons">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">شارك البلاغ:</h5>
                            <div class="blog-stats">
                                <span class="me-3">
                                    <i class="far fa-eye"></i>
                                    <?php echo number_format($blog['views']); ?> مشاهدة
                                </span>
                                <span>
                                    <i class="far fa-calendar-alt"></i>
                                    <?php echo date('Y/m/d', strtotime($blog['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                        <?php
                        $share_url = $canonical_url;
                        $share_text = $blog['title'];
                        $image_url = $og_image;
                        ?>
                        <!-- Twitter/X Share -->
                        <button onclick="window.open('https://twitter.com/intent/tweet?url=<?php echo urlencode($share_url); ?>&text=<?php echo urlencode($share_text); ?>', '_blank')" 
                                class="btn btn-twitter">
                            <i class="fab fa-twitter"></i> X (تويتر)
                        </button>
                        <!-- WhatsApp Share -->
                        <button onclick="window.open('https://api.whatsapp.com/send?text=<?php echo rawurlencode($share_text . "\n" . $share_url . "\n" . $image_url); ?>', '_blank')" 
                                class="btn btn-whatsapp">
                            <i class="fab fa-whatsapp"></i> واتساب
                        </button>
                        <!-- Copy Link with Image -->
                        <button onclick="copyToClipboard(<?php echo json_encode($share_text . "\n" . $share_url . "\n" . $image_url, JSON_HEX_TAG|JSON_HEX_AMP|JSON_HEX_APOS|JSON_HEX_QUOT); ?>)" 
                                class="btn btn-copy" 
                                id="copyButton">
                            <i class="fas fa-link"></i> نسخ الرابط
                        </button>
                        
                    </div>
                </article>
            </div>

            <!-- Related Posts -->
            <div class="col-lg-4">
                <?php if (!empty($related_blogs)): ?>
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">بلاغات مشابهة</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($related_blogs as $related): ?>
                        <div class="related-card mb-3">
                            <?php if (!empty($related['thumbnail_image'])): ?>
                            <img src="uploads/thumbnail/<?php echo htmlspecialchars($related['thumbnail_image'], ENT_QUOTES, 'UTF-8'); ?>" 
                                 class="card-img-top" loading="lazy"
                                 alt="<?php echo htmlspecialchars($related['title'], ENT_QUOTES, 'UTF-8'); ?>">
                            <?php endif; ?>
                            <div class="card-body">
                                <h6 class="card-title"><?php echo htmlspecialchars($related['title'], ENT_QUOTES, 'UTF-8'); ?></h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="far fa-calendar-alt"></i>
                                        <?php echo date('Y/m/d', strtotime($related['created_at'])); ?>
                                    </small>
                                    <a href="blog.php?id=<?php echo (int)$related['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        اقرأ المزيد
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>عن مصد</h5>
                    <p>منصة متخصصة في نشر البلاغات والتحذيرات لحماية المجتمع من عمليات الاحتيال والنصب</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>تابعنا</h5>
                    <div class="social-icons">
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> مصد - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Copy Link Script -->
    <script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            var button = document.getElementById('copyButton');
            button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(function() {
                button.innerHTML = '<i class="fas fa-link"></i> نسخ الرابط';
            }, 2000);
        });
    }

    function downloadImage(imageUrl, title) {
        fetch(imageUrl)
            .then(response => response.blob())
            .then(blob => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = title + '.jpg';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
    }
    </script>
</body>
</html> 
