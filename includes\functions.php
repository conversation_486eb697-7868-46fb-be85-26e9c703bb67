<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Build an absolute URL using current scheme/host and script base path
 * @param string $path Optional path (with or without leading slash)
 * @return string Absolute URL
 */
function base_url(string $path = ''): string {
    $scheme = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $basePath = rtrim(dirname($_SERVER['SCRIPT_NAME'] ?? ''), "/\\");
    $url = $scheme . '://' . $host . $basePath;
    if ($path !== '') {
        $url .= '/' . ltrim($path, '/');
    }
    return $url;
}

/**
 * Build absolute URL for assets or uploads
 * @param string $path Path relative to web root (e.g., 'assets/img/x.png' or 'uploads/x.jpg')
 */
function asset_url(string $path): string {
    return base_url(ltrim($path, '/'));
}

/**
 * Get latest published posts
 * @param int $limit Number of posts to return
 * @return array Array of posts
 */
function getLatestPosts($limit = 6) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT b.*, c.name as category_name, c.description as category_description,
               b.thumbnail_image, b.featured_image 
        FROM blogs b 
        LEFT JOIN categories c ON b.category_id = c.id 
        WHERE b.status = 'published'
        ORDER BY b.created_at DESC 
        LIMIT :limit
    ");
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get all categories
 * @return array Array of categories
 */
function getCategories() {
    global $pdo;
    
    $stmt = $pdo->query("
        SELECT id, name, description 
        FROM categories 
        ORDER BY name ASC
    ");
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get posts by category
 * @param int $categoryId Category ID
 * @param int $limit Number of posts to return
 * @return array Array of posts
 */
function getPostsByCategory($categoryId, $limit = 10) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM posts p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.category_id = :category_id 
        AND p.status = 'published' 
        ORDER BY p.created_at DESC 
        LIMIT :limit
    ");
    $stmt->bindValue(':category_id', $categoryId, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get single post by ID
 * @param int $postId Post ID
 * @return array|false Post data or false if not found
 */
function getPostById($postId) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM posts p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = :id 
        AND p.status = 'published'
    ");
    $stmt->bindValue(':id', $postId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Search posts by keyword
 * @param string $keyword Search keyword
 * @return array Array of matching posts
 */
function searchPosts($keyword) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM posts p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE (p.title LIKE :keyword 
        OR p.content LIKE :keyword) 
        AND p.status = 'published' 
        ORDER BY p.created_at DESC
    ");
    $stmt->bindValue(':keyword', "%$keyword%", PDO::PARAM_STR);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Increment post view count
 * @param int $postId Post ID
 */
function incrementPostViews($postId) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        UPDATE posts 
        SET views = views + 1 
        WHERE id = :id
    ");
    $stmt->bindValue(':id', $postId, PDO::PARAM_INT);
    $stmt->execute();
}

/**
 * Get popular posts based on views
 * @param int $limit Number of posts to return
 * @return array Array of popular posts
 */
function getPopularPosts($limit = 5) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM posts p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'published' 
        ORDER BY p.views DESC 
        LIMIT :limit
    ");
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
} 
