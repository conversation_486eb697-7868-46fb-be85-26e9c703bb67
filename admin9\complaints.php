<?php
session_start();
require_once '../config/db.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}
require_once '../header.php';
// Handle status updates
if (isset($_POST['action']) && isset($_POST['complaint_id'])) {
    $allowed_actions = ['approve', 'reject'];
    $complaint_id = (int)$_POST['complaint_id'];
    $action = $_POST['action'];

    if (in_array($action, $allowed_actions)) {
        $status = ($action === 'approve') ? 'approved' : 'rejected';
        try {
            $stmt = $pdo->prepare("UPDATE complaints SET status = ? WHERE id = ?");
            $stmt->execute([$status, $complaint_id]);
            $_SESSION['success'] = 'تم تحديث حالة البلاغ بنجاح';
        } catch (PDOException $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة البلاغ';
        }
    }
    header('Location: complaints.php');
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Build the query
$query = "
    SELECT c.*, cat.name as category_name 
    FROM complaints c 
    LEFT JOIN categories cat ON c.category_id = cat.id 
    WHERE 1=1
";
$params = [];

if ($status_filter !== 'all') {
    $query .= " AND c.status = ?";
    $params[] = $status_filter;
}

if ($category_filter > 0) {
    $query .= " AND c.category_id = ?";
    $params[] = $category_filter;
}

$query .= " ORDER BY c.created_at DESC";

// Get complaints
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$complaints = $stmt->fetchAll();

// Get categories for filter
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البلاغات - لوحة التحكم</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <style>
        .status-badge {
            font-size: 0.875rem;
            padding: 0.35rem 0.65rem;
        }
        .status-pending { background-color: #ffc107; }
        .status-approved { background-color: #198754; }
        .status-rejected { background-color: #dc3545; }
        .complaint-details {
            white-space: pre-wrap;
            max-height: 100px;
            overflow-y: auto;
        }
        .attachment-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: contain;
        }
    </style>
</head>
<body>
    

    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2 class="mb-4">إدارة البلاغات</h2>
                
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success">
                        <?php 
                        echo $_SESSION['success'];
                        unset($_SESSION['success']);
                        ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger">
                        <?php 
                        echo $_SESSION['error'];
                        unset($_SESSION['error']);
                        ?>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">تصفية حسب الحالة</label>
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>تمت الموافقة</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">تصفية حسب التصنيف</label>
                                <select name="category" class="form-select" onchange="this.form.submit()">
                                    <option value="0">جميع التصنيفات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo $category_filter === $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Complaints Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="complaintsTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>رقم الجوال</th>
                                        <th>التصنيف</th>
                                        <th>الرابط</th>
                                        <th>التفاصيل</th>
                                        <th>المرفق</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($complaints as $complaint): ?>
                                        <tr>
                                            <td><?php echo $complaint['id']; ?></td>
                                            <td><?php echo htmlspecialchars($complaint['name']); ?></td>
                                            <td><?php echo htmlspecialchars($complaint['mobile']); ?></td>
                                            <td><?php echo htmlspecialchars($complaint['category_name']); ?></td>
                                            <td>
                                                <a href="<?php echo htmlspecialchars($complaint['link']); ?>" target="_blank" class="btn btn-sm btn-link">
                                                    <i class="fas fa-external-link-alt"></i> فتح الرابط
                                                </a>
                                            </td>
                                            <td>
                                                <div class="complaint-details">
                                                    <?php echo nl2br(htmlspecialchars($complaint['details'])); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($complaint['attachment']): ?>
                                                    <?php
                                                    $ext = strtolower(pathinfo($complaint['attachment'], PATHINFO_EXTENSION));
                                                    if (in_array($ext, ['jpg', 'jpeg', 'png'])):
                                                    ?>
                                                        <img src="../uploads/complaints/<?php echo $complaint['attachment']; ?>" 
                                                             class="attachment-preview" 
                                                             onclick="window.open(this.src)">
                                                    <?php else: ?>
                                                        <a href="../uploads/complaints/<?php echo $complaint['attachment']; ?>" 
                                                           class="btn btn-sm btn-secondary" 
                                                           target="_blank">
                                                            <i class="fas fa-file-pdf"></i> عرض الملف
                                                        </a>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يوجد مرفق</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($complaint['created_at'])); ?></td>
                                            <td>
                                                <span class="badge status-badge status-<?php echo $complaint['status']; ?>">
                                                    <?php
                                                    $status_labels = [
                                                        'pending' => 'قيد المراجعة',
                                                        'approved' => 'تمت الموافقة',
                                                        'rejected' => 'مرفوض'
                                                    ];
                                                    echo $status_labels[$complaint['status']];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($complaint['status'] === 'pending'): ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="complaint_id" value="<?php echo $complaint['id']; ?>">
                                                        <button type="submit" name="action" value="approve" class="btn btn-success btn-sm">
                                                            <i class="fas fa-check"></i> موافقة
                                                        </button>
                                                        <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm">
                                                            <i class="fas fa-times"></i> رفض
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and DataTables -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#complaintsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
                },
                "order": [[7, "desc"]], // Sort by date column by default
                "pageLength": 25
            });
        });
    </script>
</body>
</html> 